// Global polyfills loaded as early as possible in app entrypoints
// String.prototype.replaceAll polyfill for older browsers (e.g., older Android WebView, Safari < 14)
// Spec: https://tc39.es/ecma262/#sec-string.prototype.replaceall
// This implementation supports both string and global RegExp search values.
// For RegExp, a global flag is required (same as the spec).
(function applyReplaceAllPolyfill() {
  // Guard against redefinition
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  if (typeof String.prototype.replaceAll === 'function') return;

  function escapeRegExp(str: string) {
    return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  // eslint-disable-next-line no-extend-native
  Object.defineProperty(String.prototype, 'replaceAll', {
    configurable: true,
    writable: true,
    // eslint-disable-next-line func-names
    value: function (
      search: string | RegExp,
      replacement: string | ((substring: string, ...args: any[]) => string),
    ) {
      console.log('USING POLYFILL!!!!!!!!!!!!!!!!');
      const target = String(this);

      if (search instanceof RegExp) {
        if (!search.global) {
          // Align with spec: TypeError if regex is not global
          throw new TypeError(
            'String.prototype.replaceAll called with a non-global RegExp argument',
          );
        }
        return target.replace(search, replacement as any);
      }

      const strSearch = String(search);
      // Use global, escaped regex to ensure all matches are replaced and function replacement works per match
      const pattern = strSearch === '' ? /(?:)/g : new RegExp(escapeRegExp(strSearch), 'g');
      return target.replace(pattern as any, replacement as any);
    },
  });
})();
