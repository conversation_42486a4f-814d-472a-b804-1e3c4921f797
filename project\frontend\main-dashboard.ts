// https://vitejs.dev/guide/backend-integration.html
import 'vite/modulepreload-polyfill';

import initGlobalsDynamicFormula from '@core/helpers/init-globals-dynamic-formula';

import doCreateApp from '@/do-create-app';
import '@/helpers/polyfills';
import '@/plugins/buefy-admin';
import router from '@/router';
import routerTitleChanger from '@/router/router-title-changer';
import router<PERSON>pace<PERSON>he<PERSON> from '@/router/router-workspace-checker';
import routes from '@/router/routes-admin';

/* eslint-disable import/first */
console.log(
  '%cUpPass dashboard',
  /* css */ `background: #fff; color: #4364EE; font-size: 30px; margin: 20px; padding: 20px;`,
);
// eslint-disable-next-line camelcase
window.__webpack_public_path__ = 'publicPath' in window ? `${window.publicPath}` : '/dist/';

initGlobalsDynamicFormula();

routes.forEach(r => router.addRoute(r));
routerTitle<PERSON>hanger(router);
router<PERSON><PERSON><PERSON><PERSON><PERSON>(router);

doCreateApp();
