import { withThemeByClassName } from '@storybook/addon-themes';
import type { Preview, VueRenderer } from '@storybook/vue';
import { initialize, mswLoader } from 'msw-storybook-addon';
import Vue from 'vue';
import Router from 'vue-router';

import { useDynamicFormApp } from '../packages/core/src/composables/use-dynamic-form-app';
import initGlobalsDynamicEvent from '../packages/core/src/helpers/init-globals-dynamic-event';
import initGlobalsHelpers from '../packages/core/src/helpers/init-globals-helpers';
import initGlobalsInterceptors from '../packages/core/src/helpers/init-globals-interceptors';
import i18n from '../packages/core/src/plugins/i18n';
import '../packages/helpers/src/plugins/iconify';
import '../packages/helpers/src/plugins/vue-svg-inline-plugin';
import '../src/assets/styles/app.scss';
import '../src/assets/styles/themes.scss';
import { useAppTranslation } from '../src/composables/use-app-translation';
import initDynamicEventAddons from '../src/helpers/init-dynamic-event-addons';
import initFilters from '../src/helpers/init-filters';
import initGlobalComponents from '../src/helpers/init-global-components';
import '../src/helpers/polyfills';
import '../src/plugins/axios';
import '../src/plugins/buefy-admin';
import '../src/plugins/dynamic-form';
import pinia from '../src/plugins/pinia';
import { USER_AGENT_CHOICES } from './lib/ua-parser-js.mock';

const themeEl = document.createElement('link');
themeEl.id = 'app-settings-theme-body';
themeEl.rel = 'stylesheet';
themeEl.href = 'https://cdn.uppass.io/form/themes/e1abbd51/default/style.css';
document.head.appendChild(themeEl);

(window as any).__webpack_public_path__ = '/';

Vue.config.productionTip = false;

initFilters(Vue);
initGlobalComponents(Vue);

initDynamicEventAddons();
initGlobalsHelpers();
initGlobalsInterceptors();

initGlobalsDynamicEvent();

const { loadDefaultLocale } = useAppTranslation();

loadDefaultLocale();

const { createFormApp, createFormInstance, mainInstance } = useDynamicFormApp();
createFormApp({ apiPath: {} });
const dynamicFormInstance = createFormInstance('storybook');
mainInstance.value = dynamicFormInstance;

initialize({
  onUnhandledRequest: 'bypass',
});

i18n.locale = 'en';

const preview: Preview = {
  loaders: [mswLoader],
  globalTypes: {
    userAgent: {
      description: 'User Agent',
      defaultValue: USER_AGENT_CHOICES[0].value,
      toolbar: {
        title: 'User Agent',
        items: USER_AGENT_CHOICES,
        dynamicTitle: true,
      },
    },
  },
  decorators: [
    (story, context) => {
      const userAgent = context.globals.userAgent || USER_AGENT_CHOICES[0].value;
      globalThis.UAParserInstance.setUAFromChoice(userAgent);

      return {
        components: { story },
        template: '<story />',
        router: new Router({
          mode: 'history',
          base: '/',
          routes: [],
        }),
        i18n,
        pinia,
        provide: {
          dynamicFormInstance,
          id: dynamicFormInstance.id,
          allSchema: {},
          allData: {},
          reportSection: {},
        },
      };
    },
    withThemeByClassName<VueRenderer>({
      themes: {
        default: '',
        red: 'theme-red',
      },
      defaultTheme: 'default',
    }),
  ],
};
export default preview;
