// https://vitejs.dev/guide/backend-integration.html
import 'vite/modulepreload-polyfill';

import doCreateApp from '@/do-create-app';
import '@/helpers/polyfills';
import router from '@/router';
import routes from '@/router/routes-client';

/* eslint-disable import/first */
console.log(
  '%cUpPass form',
  /* css */ `background: #4364EE; color: #fff; font-size: 30px; margin: 20px; padding: 20px;`,
);
// eslint-disable-next-line camelcase
window.__webpack_public_path__ = 'publicPath' in window ? `${window.publicPath}` : '/dist/';

routes.forEach(r => router.addRoute(r));

doCreateApp();
